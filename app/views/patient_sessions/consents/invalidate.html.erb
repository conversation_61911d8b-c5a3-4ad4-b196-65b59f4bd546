<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(session_patient_programme_consent_path, name: "consent page") %>
<% end %>

<%= form_with model: @consent, url: invalidate_session_patient_programme_consent_path, method: :post do |f| %>
  <%= f.govuk_error_summary %>

  <% page_title = "Mark response as invalid" %>
  <%= h1 page_title: do %>
    <span class="nhsuk-caption-l">
      Consent response from <%= @consent.name %>
    </span>
    <%= page_title %>
  <% end %>

  <p class="nhsuk-body">This operation cannot be undone.</p>

  <%= f.govuk_text_area :notes, label: { text: "Notes", size: "m" } %>

  <%= f.govuk_submit "Mark as invalid", warning: true %>
<% end %>
