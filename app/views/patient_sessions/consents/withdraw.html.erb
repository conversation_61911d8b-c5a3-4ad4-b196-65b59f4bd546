<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(session_patient_programme_consent_path, name: "consent page") %>
<% end %>

<%= form_with model: @consent, url: withdraw_session_patient_programme_consent_path, method: :post do |f| %>
  <%= f.govuk_error_summary %>

  <% page_title = "Withdraw consent" %>
  <%= h1 page_title: do %>
    <span class="nhsuk-caption-l">
    Consent response from <%= @consent.name %>
  </span>
    <%= page_title %>
  <% end %>

  <%= f.govuk_radio_buttons_fieldset(:reason_for_refusal,
                                     legend: { text: "Reason for refusal" }) do %>
    <%= f.govuk_radio_button :reason_for_refusal, "already_vaccinated",
                             label: { text: "Vaccine already received" }, link_errors: true %>
    <%= f.govuk_radio_button :reason_for_refusal, "will_be_vaccinated_elsewhere",
                             label: { text: "Vaccine will be given elsewhere" } %>
    <%= f.govuk_radio_button :reason_for_refusal, "medical_reasons",
                             label: { text: "Medical reasons" } %>
    <%= f.govuk_radio_button :reason_for_refusal, "personal_choice",
                             label: { text: "Personal choice" } %>
    <%= f.govuk_radio_divider %>
    <%= f.govuk_radio_button :reason_for_refusal, "other",
                             label: { text: "Other" } %>
  <% end %>

  <%= f.govuk_text_area :notes, label: { text: "Notes", size: "m" } %>

  <%= f.govuk_submit "Withdraw consent" %>
<% end %>
