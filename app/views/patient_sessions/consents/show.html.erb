<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(
        session_patient_programme_path(@session, @patient, @programme),
        name: @patient.full_name,
      ) %>
<% end %>

<%= h1 "Consent response from #{@consent.name}" %>

<ul class="app-action-list">
  <% if @consent.can_withdraw? %>
    <li class="app-action-list__item">
      <%= link_to "Withdraw consent", withdraw_session_patient_programme_consent_path %>
    </li>
  <% end %>

  <% if @consent.can_invalidate? %>
    <li class="app-action-list__item">
      <%= link_to "Mark as invalid", invalidate_session_patient_programme_consent_path %>
    </li>
  <% end %>
</ul>

<%= render AppCardComponent.new do |card| %>
  <% card.with_heading { "Consent" } %>
  <%= render AppConsentSummaryComponent.new(@consent) %>
<% end %>

<%= render AppCardComponent.new do |card| %>
  <% card.with_heading { "Child" } %>
  <%= render AppConsentPatientSummaryComponent.new(@consent) %>
<% end %>

<% if (parent_relationship = @consent.parent_relationship).present? %>
  <%= render AppParentCardComponent.new(parent_relationship:) %>
<% end %>

<% if @consent.response_given? %>
  <%= render AppHealthAnswersCardComponent.new(@consent) %>
<% end %>
