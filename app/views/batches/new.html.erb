<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(vaccines_path, name: "manage vaccines") %>
<% end %>

<% page_title = "Add batch" %>

<%= h1 page_title: do %>
  <span class="nhsuk-caption-l">
    <%= @vaccine.brand %>
  </span>
  <%= page_title %>
<% end %>

<%= form_with model: @form, url: vaccine_batches_path(@vaccine), method: :post do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_text_field :name, label: { text: "Batch" }, width: 10 %>

  <%= f.govuk_date_field :expiry,
                         legend: { text: "Expiry date", size: "s" },
                         hint: { text: "For example, 27 10 2025" },
                         link_errors: true %>

  <%= f.govuk_submit "Add batch" %>
<% end %>
