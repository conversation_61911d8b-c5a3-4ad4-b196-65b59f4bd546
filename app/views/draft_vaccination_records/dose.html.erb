<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% legend = "Did they get the full dose?" %>
<% content_for :page_title, legend %>

<%= form_with model: @draft_vaccination_record, url: wizard_path, method: :put do |f| %>
  <% content_for(:before_content) { f.govuk_error_summary } %>

  <%= f.govuk_collection_radio_buttons :full_dose,
                                       %i[true false],
                                       :itself,
                                       caption: { text: @patient.full_name, size: "l" },
                                       legend: { text: legend, tag: "h1", size: "l" } %>

  <%= f.govuk_submit %>
<% end %>
