<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(backlink_path) %>
<% end %>

<% title = "Please tell us why you do not agree to your child having the " \
   "#{@consent_form.refused_programmes.map(&:name).to_sentence} " \
   "#{"vaccination".pluralize(@consent_form.refused_programmes.count)}" %>
<% content_for :page_title, title %>

<%= form_with model: @consent_form, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= f.govuk_radio_buttons_fieldset(:reason,
                                     legend: { size: "l", text: title, tag: "h1" }) do %>
    <% if @consent_form.vaccine_may_contain_gelatine? %>
      <%= f.govuk_radio_button :reason, "contains_gelatine",
                               label: { text: "Vaccine contains gelatine from pigs" },
                               link_errors: true %>
    <% end %>
    <%= f.govuk_radio_button :reason, "already_vaccinated",
                             label: { text: "Vaccine already received" } %>
    <%= f.govuk_radio_button :reason, "will_be_vaccinated_elsewhere",
                             label: { text: "Vaccine will be given elsewhere" } %>
    <%= f.govuk_radio_button :reason, "medical_reasons",
                             label: { text: "Medical reasons" } %>
    <%= f.govuk_radio_button :reason, "personal_choice",
                             label: { text: "Personal choice" } %>
    <%= f.govuk_radio_divider %>
    <%= f.govuk_radio_button :reason, "other",
                             label: { text: "Other" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
