<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(
        health_question_backlink_path(@consent_form, @health_answer)
      ) %>
<% end %>

<% title = @health_answer.question %>
<% content_for :page_title, title %>

<%= form_with model: @health_answer, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= hidden_field_tag "question_number", @question_number %>

  <%= f.govuk_radio_buttons_fieldset :response,
                                     legend: { size: "l", text: title, tag: "h1" },
                                     hint: { text: @health_answer.hint } do %>
    <% if @health_answer.requires_notes? %>
      <%= f.govuk_radio_button :response, "yes", label: { text: "Yes" }, link_errors: true do %>
        <%= f.govuk_text_area :notes, label: { text: "Give details" } %>
      <% end %>
    <% else %>
      <%= f.govuk_radio_button :response, "yes", label: { text: "Yes" }, link_errors: true %>
    <% end %>

    <%= f.govuk_radio_button :response, "no", label: { text: "No" } %>
  <% end %>

  <%= f.govuk_submit "Continue" %>
<% end %>
