<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(vaccines_path, name: "vaccines page") %>
<% end %>

<%= h1 vaccine_heading(@vaccine), size: "l" %>

<%= render AppCardComponent.new do |c| %>
  <% c.with_heading { "Vaccine details" } %>
  <dl class="nhsuk-summary-list">
    <div class="nhsuk-summary-list__row">
      <dt class="nhsuk-summary-list__key">
        Vaccine
      </dt>
      <dd class="nhsuk-summary-list__value">
        <%= @vaccine.programme.name %>
      </dd>
    </div>
    <div class="nhsuk-summary-list__row">
      <dt class="nhsuk-summary-list__key">
        Brand
      </dt>
      <dd class="nhsuk-summary-list__value">
        <%= @vaccine.brand %>
      </dd>
    </div>
    <div class="nhsuk-summary-list__row">
      <dt class="nhsuk-summary-list__key">
        Manufacturer
      </dt>
      <dd class="nhsuk-summary-list__value">
        <%= @vaccine.manufacturer %>
      </dd>
    </div>
    <div class="nhsuk-summary-list__row">
      <dt class="nhsuk-summary-list__key">
        Method
      </dt>
      <dd class="nhsuk-summary-list__value">
        <%= @vaccine.human_enum_name(:method).humanize %>
      </dd>
    </div>
    <div class="nhsuk-summary-list__row">
      <dt class="nhsuk-summary-list__key">
        Dose
      </dt>
      <dd class="nhsuk-summary-list__value">
        <%= @vaccine.dose_volume_ml %> ml
      </dd>
    </div>
    <div class="nhsuk-summary-list__row">
      <dt class="nhsuk-summary-list__key">
        Health questions
      </dt>
      <dd class="nhsuk-summary-list__value">
        <ul class="nhsuk-list nhsuk-list--bullet">
          <% @vaccine.health_questions.each do |question| %>
            <li><%= question.title %></li>
          <% end %>
        </ul>
      </dd>
    </div>
  </dl>
<% end %>
