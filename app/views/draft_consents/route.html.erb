<% content_for :before_main do %>
  <%= render AppBacklinkComponent.new(@back_link_path) %>
<% end %>

<% title = "How was the response given?" %>
<% content_for :page_title, title %>

<%= form_with model: @draft_consent, url: wizard_path, method: :put do |f| %>
  <%= f.govuk_error_summary %>

  <%= f.govuk_collection_radio_buttons :route,
                                       Consent.verbal_routes.keys,
                                       ->(option) { option },
                                       ->(option) { Consent.human_enum_name(:route, option) },
                                       caption: { size: "l",
                                                  text: @patient.full_name },
                                       legend: { size: "l",
                                                 tag: "h1",
                                                 text: title } %>

  <%= f.govuk_submit "Continue" %>
<% end %>
