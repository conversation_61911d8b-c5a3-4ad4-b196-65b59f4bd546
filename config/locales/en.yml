en:
  activemodel:
    attributes:
    errors:
      models:
        batch_form:
          attributes:
            expiry:
              blank: Enter an expiry date
              greater_than: Enter an expiry date after %{count}
              less_than: Enter an expiry date before %{count}
              missing_day: Enter a day
              missing_month: Enter a month
              missing_year: Enter a year
              taken: This batch already exists
            name:
              blank: Enter a batch
              invalid: Enter a batch with only letters and numbers
              too_short: Enter a batch that is more than %{count} characters long
              too_long: Enter a batch that is less than %{count} characters long
        draft_class_import:
          attributes:
            session_id:
              blank: Choose which school this class list for
            year_groups:
              blank: Choose which year groups you want to import class list records for
        draft_consent:
          attributes:
            new_or_existing_contact:
              blank: Choose who you are trying to get consent from
            reason_for_refusal:
              inclusion: Choose a reason
            notes:
              blank: Enter notes
              too_long: Enter notes that are less than %{count} characters long
            parent_email:
              blank: Enter an email address
              invalid: Enter a valid email address, <NAME_EMAIL>
              too_long: Enter a email address that is less than 300 characters long
            parent_full_name:
              blank: Enter a name
              too_long: Enter a name that is less than 300 characters long
            parent_responsibility:
              inclusion: Choose whether there is parental responsibility
            parent_phone:
              blank: Enter a phone number
              invalid: Enter a valid phone number, like 07700 900 000
              too_long: Enter a phone number that is less than 300 characters long
            parent_relationship_type:
              blank: Choose a relationship
              inclusion: Choose a relationship
            parent_relationship_other_name:
              blank: Enter a relationship
              too_long: Enter a relationship that is less than 300 characters long
            response:
              inclusion: Choose if they consent
            route:
              inclusion: Choose how the response was given
            triage_notes:
              blank: Enter triage notes
              too_long: Enter triage notes that are less than 1000 characters long
            triage_status:
              blank: Choose a status
              inclusion: Choose a status
        draft_vaccination_record:
          attributes:
            batch_id:
              blank: Choose a batch
            delivery_method:
              blank: Choose a method of delivery
              inclusion: Choose a method of delivery
            delivery_site:
              blank: Choose a delivery site
              inclusion: Choose a delivery site
            full_dose:
              inclusion: Choose whether they got the full dose
            location_name:
              blank: Enter where the vaccination was given
            notes:
              too_long: Enter notes that are less than %{count} characters long
            outcome:
              inclusion: Choose an outcome
            performed_at:
              blank: Enter a date and time
              missing_day: Enter a day
              missing_month: Enter a month
              missing_year: Enter a year
              invalid: Enter a valid date and time
              less_than_or_equal_to: Enter a time in the past
        health_answer:
          attributes:
            notes:
              blank: Enter details
              too_long: Enter details that are less than 1000 characters long
            response:
              inclusion: Choose an answer
        import_duplicate_form:
          attributes:
            apply_changes:
              inclusion: Choose which record to keep
        school_move_form:
          attributes:
            action:
              inclusion: Choose whether to update the child’s record with this new information
        session_programmes_form:
          attributes:
            programme_ids:
              blank: Choose which programmes this session is part of
              inclusion: You cannot remove a programme from the session once it has been added
        vaccinate_form:
          attributes:
            administered:
              inclusion: Choose if they are ready to vaccinate
            delivery_site:
              blank: Choose where the injection will be given
            pre_screening_confirmed:
              blank: Select if the child has confirmed all pre-screening statements are true
            pre_screening_notes:
              too_long: Enter notes that are less than %{count} characters long
        vaccination_report:
          attributes:
            file_format:
              inclusion: Choose a file format
  activerecord:
    attributes:
      consent:
        reason_for_refusals:
          already_vaccinated: Vaccine already received
          medical: Medical reasons
          other: Other
          personal_choice: Personal choice
          will_be_vaccinated_elsewhere: Vaccine will be given elsewhere
        responses:
          given: consent given
          not_provided: not provided
          refused: consent refused
        routes:
          in_person: In person
          paper: Paper
          phone: By phone
          website: Online
      consent_form:
        contact_method_types:
          any: I do not have specific needs
          other: Other
          text: I can only receive text messages
          voice: I can only receive voice calls
        reasons:
          already_vaccinated: Vaccine already received
          contains_gelatine: Vaccine contains gelatine from pigs
          medical_reasons: Medical reasons
          other: Other
          personal_choice: Personal choice
          will_be_vaccinated_elsewhere: Vaccine will be given elsewhere
      notify_log_entry:
        types:
          email: Email
          sms: SMS
      parent:
        contact_method_types:
          any: No specific needs
          other: Other
          text: Can only receive text messages
          voice: Can only receive voice calls
      parent_relationship:
        types:
          father: dad
          mother: mum
      patient_session:
        outcomes:
          could_not_vaccinate: Could not vaccinate
          no_consent: No consent
          no_outcome: No outcome yet
          vaccinated: Vaccinated
      programme:
        types:
          flu: Flu
          hpv: HPV
          menacwy: MenACWY
          td_ipv: Td/IPV
      school_move:
        sources:
          class_list_import: Class list
          cohort_import: Cohort
          parental_consent_form: Consent response
      triage:
        statuses:
          delay_vaccination: Delay vaccination to a later date
          do_not_vaccinate: Do not vaccinate in programme
          needs_follow_up: Keep in triage
          ready_to_vaccinate: Safe to vaccinate
      vaccination_record:
        delivery_methods:
          intranasal: Intramuscular (IM)
        delivery_sites:
          left_arm_lower_position: Left arm (lower position)
          left_arm_upper_position: Left arm (upper position)
          right_arm_lower_position: Right arm (lower position)
          right_arm_upper_position: Right arm (upper position)
        outcomes:
          absent_from_session: Absent from session
          administered: Vaccinated
          already_had: Already had the vaccine
          contraindications: Had contraindications
          not_well: Unwell
          refused: Refused vaccine
      vaccine:
        methods:
          injection: Injection
          nasal: Nasal spray
    errors:
      models:
        class_import:
          attributes:
            csv:
              blank: Choose a file
              empty: Choose a CSV file with at least one record
              invalid: Choose a CSV file in the correct format
        cohort_import:
          attributes:
            csv:
              blank: Choose a file
              empty: Choose a CSV file with at least one record
              invalid: Choose a CSV file in the correct format
        consent:
          attributes:
            notes:
              blank: Enter notes
              too_long: Enter notes that are less than %{count} characters long
        consent_form:
          attributes:
            address_line_1:
              blank: Enter the first line of your address
              too_long: >-
                Enter a first line of address that is less than 300 characters
                long
            address_line_2:
              too_long: >-
                Enter a second line of address that is less than 300 characters
                long
            address_postcode:
              blank: Enter a postcode
            address_town:
              blank: Enter a town or city
              too_long: Enter a town or city that is less than 300 characters long
            date_of_birth:
              blank: Enter their date of birth
              greater_than_or_equal_to: The child cannot be older than 22. Enter a date after %{count}.
              less_than: The date is in the future. Enter a date in the past.
              less_than_or_equal_to: The child cannot be younger than 3. Enter a date before %{count}.
              missing_day: Enter a day
              missing_month: Enter a month
              missing_year: Enter a year
            education_setting:
              blank: Choose if your child is home-schooled
              inclusion: Choose if your child is home-schooled
            family_name:
              blank: Enter a last name
              too_long: Enter a last name that is less than 300 characters long
            given_name:
              blank: Enter a first name
              too_long: Enter a first name that is less than 300 characters long
            notes:
              blank: Enter notes
              too_long: Enter notes that are less than %{count} characters long
            parent_contact_method_type:
              inclusion: Choose a contact method
            parent_contact_method_other_details:
              blank: Enter details about how to contact you
              too_long: Enter details that are less than 300 characters long
            parent_email:
              blank: Enter your email address
              invalid: Enter a valid email address, <NAME_EMAIL>
              too_long: Enter an email address that is less than 300 characters long
            parent_full_name:
              blank: Enter your name
              too_long: Enter a name that is less than 300 characters long
            parent_phone:
              blank: Enter a phone number
              invalid: Enter a valid phone number, like 07700 900 000
              too_long: Enter a phone number that is less than 300 characters long
            parent_relationship_type:
              blank: Choose a relationship
              inclusion: Choose a relationship
            parent_relationship_other_name:
              blank: Enter your relationship
              too_long: Enter a relationship that is less than 300 characters long
            parental_responsibility:
              inclusion: You need parental responsibility to give consent
            preferred_family_name:
              blank: Enter a preferred last name
              too_long: Enter a name that is less than 300 characters long
            preferred_given_name:
              blank: Enter a preferred first name
              too_long: Enter a name that is less than 300 characters long
            reason:
              blank: Choose a reason
            reason_notes:
              blank: Enter details for refusing
              too_long: >-
                Enter details for refusing that are less than 1000 characters
                long
            response:
              blank: Choose if you consent
            school_confirmed:
              inclusion: Tell us if this is their school
            school_id:
              blank: Choose a school
              inclusion: Choose a school from the list
            use_preferred_name:
              inclusion: Tell us whether they use a different name
        gillick_assessment:
          attributes:
            knows_vaccination:
              inclusion: Choose whether the child knows which vaccination they will have
            knows_disease:
              inclusion: Choose whether the child knows which disease the vaccination protects against
            knows_consequences:
              inclusion: Choose whether the child knows what could happen if they got the disease
            knows_delivery:
              inclusion: Choose whether the child knows how the injection will be given
            knows_side_effects:
              inclusion: Choose whether the child knows which side effects they might experience
            notes:
              too_long: Enter notes that are less than %{count} characters long
        immunisation_import:
          attributes:
            csv:
              blank: Choose a file
              empty: Choose a CSV file with at least one record
              invalid: Choose a CSV file in the correct format
        offline_password:
          attributes:
            password:
              blank: Enter a password
              too_long: Enter a password that is less than 300 characters long
              too_short: Enter a password that is at least 12 characters long
            password_confirmation:
              confirmation: The password and confirmation do not match
        parent:
          attributes:
            contact_method_type:
              inclusion: Choose a contact method
            contact_method_other_details:
              blank: Enter details about how to contact the parent
              too_long: Enter details that are less than 300 characters long
            email:
              blank: Enter an email address
            phone:
              blank: Enter a phone number
        parent_relationship:
          attributes:
            type:
              blank: Choose a relationship
              inclusion: Choose a relationship
            other_name:
              blank: Enter a relationship
              too_long: Enter a relationship that is less than 300 characters long
        patient:
          attributes:
            nhs_number:
              invalid: Enter a valid NHS number
              wrong_length: Enter a valid NHS number with 10 characters
              taken: NHS number is already assigned to a different patient
        pre_screening:
          attributes:
            notes:
              too_long: Enter notes that are less than %{count} characters long
        programme:
          attributes:
            type:
              blank: Choose a programme type
              inclusion: Choose a programme type
            vaccines:
              blank: Choose the vaccines this programme administers
              match_type: Vaccines must be suitable for the programme type
        session:
          attributes:
            send_consent_requests_at:
              blank: Enter a date
              missing_day: Enter a day
              missing_month: Enter a month
              missing_year: Enter a year
              greater_than_or_equal_to: Enter a date at most 3 months before the first session date (%{count})
              less_than_or_equal_to: Enter a date before the first session date and first reminder (%{count})
            send_invitations_at:
              blank: Enter a date
              missing_day: Enter a day
              missing_month: Enter a month
              missing_year: Enter a year
              greater_than_or_equal_to: Enter a date at most 3 months before the first session date (%{count})
              less_than: Enter a date before the first session date (%{count})
            weeks_before_consent_reminders:
              blank: Enter weeks before a session takes place
              greater_than_or_equal_to: Enter %{count} or more weeks before a session
              less_than_or_equal_to: Enter %{count} or fewer weeks before a session
        session_date:
          attributes:
            value:
              blank: Enter a date
              missing_day: Enter a day
              missing_month: Enter a month
              missing_year: Enter a year
              taken: Enter a different date to the other session dates
              greater_than_or_equal_to: Enter a date on or after the start of the school year (%{count})
              less_than_or_equal_to: Enter a date on or before the end of the current school year (%{count})
        triage:
          attributes:
            notes:
              blank: Enter triage notes
              too_long: Enter triage notes that are less than 1000 characters long
            status:
              blank: Choose a status
              inclusion: Choose a status
        user:
          attributes:
            email:
              blank: Enter an email address
              invalid: Enter a valid email address, <NAME_EMAIL>
              taken: This email address is already in use
              too_long: Enter an email address that is less than 255 characters long
            family_name:
              blank: Enter your family name
              too_long: Enter a family name that is less than 255 characters long
            given_name:
              blank: Enter your given name
              too_long: Enter a given name that is less than 255 characters long
            ods_code:
              blank: Enter an ODS code
            password:
              blank: Enter a password
              too_long: Enter a password that is less than 128 characters long
              too_short: Enter a password that is at least 10 characters long
            password_confirmation:
              confirmation: The password and confirmation do not match
            unlock_token:
              invalid: The unlock token is invalid
        vaccination_record:
          attributes:
            performed_at:
              less_than_or_equal_to: Enter a time in the past
  children:
    zero: No children
    one: 1 child
    other: "%{count} children"
  cohorts:
    index:
      title: Cohorts
  consent_forms:
    index:
      title: Unmatched consent responses
      title_short: Unmatched responses
    confirm:
      consent_card_title:
        flu: Consent for the flu vaccination
        hpv: Consent for the HPV vaccination
        menacwy: Consent for the MenACWY vaccination
        td_ipv: Consent for the Td/IPV vaccination
        menacwy_td_ipv: Consent for the MenACWY and Td/IPV vaccinations
      i_agree: Consent given
    consent:
      hint:
        flu: The nasal flu spray contains gelatine which comes from pigs.
        hpv: ""
        menacwy: ""
        td_ipv: ""
      i_agree:
        flu: Yes, I agree to them having a nasal vaccine
        hpv: Yes, I agree
        menacwy: Yes, I agree to them having both vaccinations
        td_ipv: Yes, I agree to them having both vaccinations
      title:
        flu: Do you agree to your child having the nasal flu vaccination?
        hpv: Do you agree to your child having the Human papillomavirus (HPV) vaccination?
        menacwy: Do you agree to your child having the MenACWY and Td/IPV (3-in-1 teenage booster) vaccinations?
        td_ipv: Do you agree to your child having the MenACWY and Td/IPV (3-in-1 teenage booster) vaccinations?
    reason_notes:
      title:
        already_vaccinated: Where did your child get their vaccination?
        contains_gelatine: Tell us why you don’t agree
        medical_reasons: What medical reasons prevent your child from being vaccinated?
        other: Tell us why you don’t agree
        personal_choice: Tell us why you don’t agree
        will_be_vaccinated_elsewhere: Where will your child get their vaccination?
    start:
      vaccines:
        flu:
          title: Flu
          description:
            - >-
              The vaccination helps to protect children against flu. It also
              protects others who are vulnerable to flu, such as babies and
              older people.
        hpv:
          title: HPV
          description:
            - >-
              The HPV vaccine helps to prevent HPV related cancers from
              developing in boys and girls.
            - >-
              The number of doses you need depends on your age and how well your
              immune system works. Young people usually only need 1 dose.
            - >-
              <a href="https://www.nhs.uk/conditions/vaccinations/hpv-human-papillomavirus-vaccine/">Find
              out more about the HPV vaccine</a>
        menacwy:
          title: MenACWY
          description:
            - >-
              The MenACWY vaccine helps protect against meningitis and sepsis.
              It is recommended for all teenagers. Most people only need one
              dose of the vaccine.
            - >-
              <a href="https://www.nhs.uk/vaccinations/menacwy-vaccine/">Find
              out more about the MenACWY vaccine</a>
        td_ipv:
          title: Td/IPV (3-in-1 teenage booster)
          description:
            - >-
              The Td/IPV vaccine (also called the 3-in-1 teenage booster) helps
              protect against tetanus, diphtheria and polio.
            - >-
              It boosts the protection provided by the
              <a href="https://www.nhs.uk/vaccinations/6-in-1-vaccine/">6-in-1
              vaccine</a> and
              <a href="https://www.nhs.uk/vaccinations/4-in-1-preschool-booster-vaccine/">4-in-1
              pre-school booster vaccine</a>.
            - >-
              <a href="https://www.nhs.uk/vaccinations/td-ipv-vaccine-3-in-1-teenage-booster/">Find
              out more about the Td/IPV vaccine</a>
      title: Give or refuse consent for vaccinations
  dashboard:
    index:
      title: Home
      notices:
        header: Important notices
        description:
          one: "%{count} important notice needs attention"
          other: "%{count} important notices need attention"
  draft_consents:
    agree:
      title:
        flu: Do they agree to them having the flu vaccination?
        hpv: Do they agree to them having the HPV vaccination?
        menacwy: Do they agree to them having the MenACWY vaccination?
        td_ipv: Do they agree to them having the Td/IPV vaccination?
    notes:
      title:
        already_vaccinated: Where did their child get their vaccination?
        contains_gelatine: Why are they refusing to give consent?
        medical_reasons: What medical reasons prevent their child from being vaccinated?
        other: Why are they refusing to give consent?
        personal_choice: Why are they refusing to give consent?
        will_be_vaccinated_elsewhere: Where will their child get their vaccination?
  hosting_environment: This is a %{name} environment. Do not use it to make clinical decisions.
  imports:
    index:
      title: Import records
      title_short: Import
    notices:
      index:
        no_results: There are currently no important notices.
  mailers:
    consent_form_mailer:
      reasons_for_refusal:
        already_vaccinated: they have already received the vaccine
        contains_gelatine: of the gelatine in the nasal spray
        medical_reasons: of medical reasons
        other: of other reasons
        personal_choice: of personal choice
        will_be_vaccinated_elsewhere: they will be given the vaccine elsewhere
    vaccination_mailer:
      reasons_did_not_vaccinate:
        absent_from_session: they were not in the vaccination session
        already_had: they've already had the vaccine
        contraindications: they had contraindications
        not_well: the nurse decided %{short_patient_name} was not well
        refused: they refused when the nurse offered the vaccination
  notification_banner:
    info: Information
    success: Success
    warning: Warning
  ordinal_number:
    "0": zeroth
    "1": first
    "2": second
    "3": third
    "4": fourth
    "5": fifth
    "6": sixth
    "7": seventh
    "8": eighth
    "9": ninth
    "10": tenth
  page_titles:
    accessibility_statement: Accessibility statement
  patients:
    index:
      title: Children
  programmes:
    index:
      title: Programmes
    sessions:
      table_headings:
        completed: All sessions completed
        scheduled: Sessions scheduled
        unscheduled: No sessions scheduled
  school_moves:
    index:
      title: School moves
  service:
    email: <EMAIL>
    guide:
      title: Service guidance
  sessions:
    index:
      title: Sessions
      table_heading:
        zero: There are no sessions scheduled for today.
        one: 1 session today
        other: "%{count} sessions today"
    completed:
      table_heading:
        zero: There are no locations with all sessions completed.
        one: 1 location with all sessions completed
        other: "%{count} locations with all sessions completed"
    scheduled:
      table_heading:
        zero: There are no locations with sessions scheduled.
        one: 1 location with sessions scheduled
        other: "%{count} locations with sessions scheduled"
    unscheduled:
      table_heading:
        zero: There are no locations with no sessions scheduled.
        one: 1 location with no sessions scheduled
        other: "%{count} locations with no sessions scheduled"
    tabs:
      overview: Overview
      consent: Consent
      triage: Triage
      register: Register
      record: Record vaccinations
      outcome: Session outcomes
  table:
    no_filtered_results: We couldn’t find any children that matched your filters.
    no_results: No results
  organisations:
    show:
      title: Your organisation
  vaccination_records:
    index:
      title: Vaccinations
  vaccines:
    index:
      title: Vaccines
  attendance_flash:
    absent: "%{name} is absent from today’s session."
    not_registered: "%{name} is not registered yet."
    present: "%{name} is attending today’s session."
  vaccinations_given:
    zero: No vaccinations given
    one: 1 vaccination given
    other: "%{count} vaccinations given"
  wicked:
    address: address
    agree: agree
    batch: batch
    cohort: cohort
    confirm: confirm
    confirm_school: confirm-school
    consent: consent
    contact_method: contact-method
    date_and_time: date-and-time
    date_of_birth: date-of-birth
    dates: dates
    delivery: delivery
    dose: dose
    education_setting: education-setting
    file_format: file-format
    gp: gp
    health_question: health-question
    location: location
    name: name
    notes: notes
    notify_parents: notify-parents
    outcome: outcome
    parent: parent
    parent_details: parent-details
    questions: questions
    reason: reason
    reason_notes: reason-notes
    route: route
    school: school
    session: session
    timeline: timeline
    triage: triage
    vaccine: vaccine
    when: when
    who: who
    year_groups: year-groups
