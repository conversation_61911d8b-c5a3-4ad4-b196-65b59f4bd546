*.gem
*.rbc
/.config
/coverage/
/InstalledFiles
/pkg/
/spec/reports/
/spec/examples.txt
/test/tmp/
/test/version_tmp/
/tmp/

# Ignore Byebug command history file.
.byebug_history

## Documentation cache and generated files:
/.yardoc/
/_yardoc/
/doc/
/rdoc/

## Environment normalization:
/.bundle/
/vendor/bundle
/lib/bundler/man/

*.log
.DS_Store
.bundle
.env
.env.local
.envrc
.idea
.vscode
config/master.key
db/*.sqlite3
node_modules
public/assets

app/assets/builds/*
!app/assets/builds/.keep
public/*.js
public/*.js.map

log/*
!log/.keep

tmp/*
!tmp/.keep

tmp/pids/*
!tmp/pids/
!tmp/pids/.keep

cypress/screenshots
cypress/videos

config/settings.local.yml
config/settings/*.local.yml
config/environments/*.local.yml

coverage
/test-results/
/playwright-report/
/playwright/.cache/

docs/.asciidoctor
docs/diagrams/images

.terraform
*.tfstate
*.tfstate.backup

/config/credentials/*.key
!/config/credentials/development.key
!/config/credentials/test.key

# used by RSpec
spec/examples.txt

# Local scratch workspace
scratchpad

# Artillery temporarily bundles TypeScript files in this directory
tests/dist

# These files are autogenerated by the deploy GitHub Action
public/sha
public/ref
