name: Build and push image

on:
  workflow_dispatch:
  workflow_call:
    inputs:
      git-sha:
        description: The git commit sha to build the image from.
        type: string

concurrency:
  group: build-and-push-image-${{ inputs.git-sha || github.sha }}

env:
  PUSH_IMAGE_TO_PRODUCTION: ${{ github.ref_name == 'main' }}

permissions: {}

jobs:
  check-image-presence:
    name: Check if images already exist
    runs-on: ubuntu-latest
    permissions:
      id-token: write
    outputs:
      build-needed: ${{ steps.check-dev-image.outputs.build-needed || steps.check-prod-image.outputs.build-needed }}
    steps:
      - name: Configure AWS Dev Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::393416225559:role/GithubDeployMavisAndInfrastructure
          aws-region: eu-west-2
      - name: Check if dev image exists
        id: check-dev-image
        run: |
          if aws ecr describe-images --repository-name mavis/webapp --image-ids imageTag=${{ inputs.git-sha || github.sha }} > /dev/null 2>&1; then
            echo "Dev image with given tag already exists"
          else
            echo "Dev image does not exist. Build needed"
            echo "build-needed=true" >> $GITHUB_OUTPUT
          fi
      - name: Configure AWS Production credentials
        if: env.PUSH_IMAGE_TO_PRODUCTION == 'true'
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: arn:aws:iam::820242920762:role/GithubDeployMavisAndInfrastructure
          aws-region: eu-west-2
      - name: Check if production image exists
        if: env.PUSH_IMAGE_TO_PRODUCTION == 'true'
        id: check-prod-image
        run: |
          if aws ecr describe-images --repository-name mavis/webapp --image-ids imageTag=${{ inputs.git-sha || github.sha }} > /dev/null 2>&1; then
            echo "Production image with given tag already exists"
          else
            echo "Production image does not exist. Build needed"
            echo "build-needed=true" >> $GITHUB_OUTPUT
          fi

  build:
    needs: check-image-presence
    if: needs.check-image-presence.outputs.build-needed == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ inputs.git-sha || github.sha }}
      - name: Write build SHA
        run: git rev-parse HEAD > public/sha
      - name: Build Docker image
        run: docker build -t "mavis:latest" .
      - name: Save Docker image
        run: docker save -o image.tar mavis:latest
      - name: Upload Docker image
        uses: actions/upload-artifact@v4
        with:
          name: image
          path: image.tar
  define-matrix:
    name: Determine AWS roles to push the image
    runs-on: ubuntu-latest
    needs: check-image-presence
    outputs:
      aws-roles: ${{ steps.determine-aws-roles.outputs.aws-roles }}
    steps:
      - name: Set aws roles
        id: determine-aws-roles
        run: |
          if [ ${{ env.PUSH_IMAGE_TO_PRODUCTION }} = 'true' ]; then
            echo 'aws-roles=["arn:aws:iam::393416225559:role/GithubDeployMavisAndInfrastructure", "arn:aws:iam::820242920762:role/GithubDeployMavisAndInfrastructure"]' >> $GITHUB_OUTPUT
          else
            echo 'aws-roles=["arn:aws:iam::393416225559:role/GithubDeployMavisAndInfrastructure"]' >> $GITHUB_OUTPUT
          fi
  push:
    runs-on: ubuntu-latest
    needs: [build, define-matrix]
    permissions:
      id-token: write
    strategy:
      matrix:
        aws-role: ${{ fromJSON(needs.define-matrix.outputs.aws-roles) }}
    steps:
      - name: Download Docker image
        uses: actions/download-artifact@v4
        with:
          name: image
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          role-to-assume: ${{ matrix.aws-role }}
          aws-region: eu-west-2
      - name: Login to ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      - name: Load Docker image
        run: docker load -i image.tar
      - name: Tag Docker image
        run: docker tag mavis:latest "${{ steps.login-ecr.outputs.registry }}/mavis/webapp":"${{ inputs.git-sha || github.sha }}"
      - name: Push Docker image
        run: docker push "${{ steps.login-ecr.outputs.registry }}/mavis/webapp":"${{ inputs.git-sha || github.sha }}"
