GIT
  remote: https://github.com/citizensadvice/capybara_accessible_selectors
  revision: a55fca198e43a3c81981ae46412c678a57f4f2f5
  branch: main
  specs:
    capybara_accessible_selectors (0.11.0)
      capybara (~> 3.36)

GIT
  remote: https://github.com/tvararu/audited
  revision: e089773f10f8ef5e5e92a2052a2fea55f83091d2
  branch: encryption
  specs:
    audited (5.8.0)
      activerecord (>= 5.2, < 8.2)
      activesupport (>= 5.2, < 8.2)

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
    actionmailer (8.0.2)
      actionpack (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activesupport (= 8.0.2)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (8.0.2)
      actionview (= 8.0.2)
      activesupport (= 8.0.2)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (8.0.2)
      actionpack (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (8.0.2)
      activesupport (= 8.0.2)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (8.0.2)
      activesupport (= 8.0.2)
      globalid (>= 0.3.6)
    activemodel (8.0.2)
      activesupport (= 8.0.2)
    activerecord (8.0.2)
      activemodel (= 8.0.2)
      activesupport (= 8.0.2)
      timeout (>= 0.4.0)
    activerecord-import (2.2.0)
      activerecord (>= 4.2)
    activerecord-session_store (2.2.0)
      actionpack (>= 7.0)
      activerecord (>= 7.0)
      cgi (>= 0.3.6)
      rack (>= 2.0.8, < 4)
      railties (>= 7.0)
    activestorage (8.0.2)
      actionpack (= 8.0.2)
      activejob (= 8.0.2)
      activerecord (= 8.0.2)
      activesupport (= 8.0.2)
      marcel (~> 1.0)
    activesupport (8.0.2)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    aes_key_wrap (1.1.0)
    amazing_print (1.7.2)
    annotaterb (4.14.0)
    asciidoctor (2.0.23)
    asciidoctor-diagram (3.0.0)
      asciidoctor (>= 1.5.7, < 3.x)
      rexml
    ast (2.4.3)
    attr_required (1.0.2)
    aws-eventstream (1.4.0)
    aws-partitions (1.1114.0)
    aws-sdk-accessanalyzer (1.72.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.225.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-ec2 (1.530.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-ecr (1.103.0)
      aws-sdk-core (~> 3, >= 3.225.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-iam (1.120.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-kms (1.101.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-rds (1.277.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.186.0)
      aws-sdk-core (~> 3, >= 3.216.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.0)
      aws-eventstream (~> 1, >= 1.0.2)
    backport (1.2.0)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bindata (2.5.0)
    bindex (0.8.1)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.0.2)
      racc
    builder (3.3.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    capybara-screenshot (1.0.26)
      capybara (>= 1.0, < 4)
      launchy
    caxlsx (4.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    cgi (0.4.2)
    charlock_holmes (0.7.9)
    childprocess (5.0.0)
    climate_control (1.2.0)
    coderay (1.1.3)
    concurrent-ruby (1.3.5)
    config (5.5.2)
      deep_merge (~> 1.2, >= 1.2.1)
      ostruct
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    cssbundling-rails (1.4.1)
      railties (>= 6.0.0)
    csv (3.3.5)
    cuprite (0.15.1)
      capybara (~> 3.0)
      ferrum (~> 0.15.0)
    date (3.4.1)
    debug (1.10.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    deep_merge (1.2.2)
    devise (4.9.4)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.2)
    discard (1.4.0)
      activerecord (>= 4.2, < 9.0)
    docile (1.4.0)
    domain_name (0.6.20240107)
    drb (2.2.3)
    dry-cli (1.2.0)
    email_validator (2.2.4)
      activemodel
    erb (5.0.1)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_instruments (1.2.0)
      activerecord (>= 4.0)
      factory_bot (>= 4)
    factory_bot_rails (6.4.4)
      factory_bot (~> 6.5)
      railties (>= 5.0.0)
    faker (3.5.1)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.1)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-net_http (3.4.0)
      net-http (>= 0.5.0)
    ferrum (0.15)
      addressable (~> 2.5)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.17.1-arm64-darwin)
    ffi (1.17.1-x86_64-linux-gnu)
    flipper (1.3.4)
      concurrent-ruby (< 2)
    flipper-active_record (1.3.4)
      activerecord (>= 4.2, < 9)
      flipper (~> 1.3.4)
    flipper-ui (1.3.4)
      erubi (>= 1.0.0, < 2.0.0)
      flipper (~> 1.3.4)
      rack (>= 1.4, < 4)
      rack-protection (>= 1.5.3, < 5.0.0)
      rack-session (>= 1.0.2, < 3.0.0)
      sanitize (< 8)
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    good_job (4.10.1)
      activejob (>= 6.1.0)
      activerecord (>= 6.1.0)
      concurrent-ruby (>= 1.3.1)
      fugit (>= 1.11.0)
      railties (>= 6.1.0)
      thor (>= 1.0.0)
    govuk-components (5.10.0)
      html-attributes-utils (~> 1.0.0, >= 1.0.0)
      pagy (>= 6, < 10)
      view_component (>= 3.18, < 3.22)
    govuk_design_system_formbuilder (5.10.1)
      actionview (>= 6.1)
      activemodel (>= 6.1)
      activesupport (>= 6.1)
      html-attributes-utils (~> 1)
    govuk_markdown (2.0.3)
      activesupport
      redcarpet
    haml (6.1.1)
      temple (>= 0.8.2)
      thor
      tilt
    hashdiff (1.1.2)
    hashie (5.0.0)
    hotwire-livereload (2.0.0)
      actioncable (>= 7.0.0)
      listen (>= 3.0.0)
      railties (>= 7.0.0)
    html-attributes-utils (1.0.2)
      activesupport (>= 6.1.4.4)
    htmlentities (4.3.4)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    its (0.2.0)
      rspec-core
    jaro_winkler (1.6.1)
    jmespath (1.6.2)
    jsbundling-rails (1.3.1)
      railties (>= 6.0.0)
    json (2.12.0)
    json-jwt (1.16.6)
      activesupport (>= 4.2)
      aes_key_wrap
      base64
      bindata
      faraday (~> 2.0)
      faraday-follow_redirects
    jsonb_accessor (1.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
      pg (>= 0.18.1)
    jwt (2.10.1)
      base64
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    language_server-protocol (********)
    launchy (3.0.0)
      addressable (~> 2.8)
      childprocess (~> 5.0)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    mechanize (2.14.0)
      addressable (~> 2.8)
      base64
      domain_name (~> 0.5, >= 0.5.20190701)
      http-cookie (~> 1.0, >= 1.0.3)
      mime-types (~> 3.3)
      net-http-digest_auth (~> 1.4, >= 1.4.1)
      net-http-persistent (>= 2.5.2, < 5.0.dev)
      nkf
      nokogiri (~> 1.11, >= 1.11.2)
      rubyntlm (~> 0.6, >= 0.6.3)
      webrick (~> 1.7)
      webrobots (~> 0.1.2)
    method_source (1.1.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2025.0107)
    mini_mime (1.1.5)
    minitest (5.25.5)
    msgpack (1.8.0)
    net-http (0.6.0)
      uri
    net-http-digest_auth (1.4.1)
    net-http-persistent (4.0.5)
      connection_pool (~> 2.2)
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    nio4r (2.7.4)
    nkf (0.2.0)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    notifications-ruby-client (6.2.0)
      jwt (>= 1.5, < 3)
    observer (0.1.2)
    okcomputer (1.19.0)
    omniauth (2.1.2)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    omniauth_openid_connect (0.8.0)
      omniauth (>= 1.9, < 3)
      openid_connect (~> 2.2)
    openid_connect (2.3.0)
      activemodel
      attr_required (>= 1.0.0)
      email_validator
      faraday (~> 2.0)
      faraday-follow_redirects
      json-jwt (>= 1.16)
      mail
      rack-oauth2 (~> 2.2)
      swd (~> 2.0)
      tzinfo
      validate_url
      webfinger (~> 2.0)
    orm_adapter (0.5.0)
    ostruct (0.6.1)
    pagy (9.3.4)
    parallel (1.27.0)
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.9)
    phonelib (0.10.9)
    pp (0.6.2)
      prettyprint
    prettier_print (1.2.1)
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.1.0)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
      railties (>= 7.0.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.1)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (3.1.16)
    rack-oauth2 (2.2.1)
      activesupport
      attr_required
      faraday (~> 2.0)
      faraday-follow_redirects
      json-jwt (>= 1.11.0)
      rack (>= 2.1.0)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (2.2.1)
      rack (>= 3)
    rails (8.0.2)
      actioncable (= 8.0.2)
      actionmailbox (= 8.0.2)
      actionmailer (= 8.0.2)
      actionpack (= 8.0.2)
      actiontext (= 8.0.2)
      actionview (= 8.0.2)
      activejob (= 8.0.2)
      activemodel (= 8.0.2)
      activerecord (= 8.0.2)
      activestorage (= 8.0.2)
      activesupport (= 8.0.2)
      bundler (>= 1.15.0)
      railties (= 8.0.2)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_semantic_logger (4.17.0)
      rack
      railties (>= 5.1)
      semantic_logger (~> 4.16)
    railties (8.0.2)
      actionpack (= 8.0.2)
      activesupport (= 8.0.2)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rbs (3.9.4)
      logger
    rdoc (6.14.0)
      erb
      psych (>= 4.0.0)
    redcarpet (3.6.1)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    reverse_markdown (3.0.0)
      nokogiri
    rexml (3.4.1)
    rladr (1.2.0)
    rspec (3.13.1)
      rspec-core (~> 3.13.0)
      rspec-expectations (~> 3.13.0)
      rspec-mocks (~> 3.13.0)
    rspec-core (3.13.4)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-html-matchers (0.10.0)
      nokogiri (~> 1)
      rspec (>= 3.0.0.a)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.0)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.75.2)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-capybara (2.22.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-govuk (5.1.4)
      rubocop (= 1.75.2)
      rubocop-ast (= 1.44.0)
      rubocop-capybara (= 2.22.1)
      rubocop-rails (= 2.31.0)
      rubocop-rake (= 0.7.1)
      rubocop-rspec (= 3.5.0)
    rubocop-rails (2.31.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rake (0.7.1)
      lint_roller (~> 1.1)
      rubocop (>= 1.72.1)
    rubocop-rspec (3.5.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-prof (1.7.1)
    ruby-progressbar (1.13.0)
    rubyXL (3.4.33)
      nokogiri (>= 1.10.8)
      rubyzip (>= 1.3.0)
    rubyntlm (0.6.5)
      base64
    rubyzip (2.4.1)
    rufo (0.18.1)
    sanitize (7.0.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.16.8)
    securerandom (0.4.1)
    semantic_logger (4.16.1)
      concurrent-ruby (~> 1.0)
    sentry-rails (5.24.0)
      railties (>= 5.0)
      sentry-ruby (~> 5.24.0)
    sentry-ruby (5.24.0)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    solargraph (0.54.5)
      backport (~> 1.2)
      benchmark (~> 0.4)
      bundler (~> 2.0)
      diff-lcs (~> 1.4)
      jaro_winkler (~> 1.6, >= 1.6.1)
      kramdown (~> 2.3)
      kramdown-parser-gfm (~> 1.1)
      logger (~> 1.6)
      observer (~> 0.1)
      ostruct (~> 0.6)
      parser (~> 3.0)
      rbs (~> 3.3)
      reverse_markdown (~> 3.0)
      rubocop (~> 1.38)
      thor (~> 1.0)
      tilt (~> 2.0)
      yard (~> 0.9, >= 0.9.24)
      yard-solargraph (~> 0.1)
    solargraph-rails (1.1.0)
      activesupport
      solargraph
    splunk-sdk-ruby (1.0.5)
    stackprof (0.2.27)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    stringio (3.1.7)
    swd (2.0.3)
      activesupport (>= 3)
      attr_required (>= 0.0.5)
      faraday (~> 2.0)
      faraday-follow_redirects
    syntax_tree (6.2.0)
      prettier_print (>= 1.2.0)
    syntax_tree-haml (4.0.3)
      haml (>= 5.2)
      prettier_print (>= 1.2.1)
      syntax_tree (>= 6.0.0)
    syntax_tree-rbs (1.0.0)
      prettier_print
      rbs
      syntax_tree (>= 2.0.1)
    temple (0.10.0)
    thor (1.3.2)
    thruster (0.1.13-arm64-darwin)
    thruster (0.1.13-x86_64-linux)
    tilt (2.6.0)
    timeout (0.4.3)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uk_postcode (2.1.8)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    validate_url (1.0.15)
      activemodel (>= 3.0.0)
      public_suffix
    view_component (3.21.0)
      activesupport (>= 5.2.0, < 8.1)
      concurrent-ruby (~> 1.0)
      method_source (~> 1.0)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webfinger (2.1.3)
      activesupport
      faraday (~> 2.0)
      faraday-follow_redirects
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    webrobots (0.1.2)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wicked (2.0.0)
      railties (>= 3.0.7)
    with_advisory_lock (5.3.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yard (0.9.37)
    yard-solargraph (0.1.0)
      yard (~> 0.9)
    zeitwerk (2.7.3)

PLATFORMS
  arm64-darwin-23
  arm64-darwin-24
  x86_64-linux

DEPENDENCIES
  activerecord-import
  activerecord-session_store
  amazing_print
  annotaterb
  asciidoctor
  asciidoctor-diagram
  audited!
  aws-sdk-accessanalyzer (~> 1)
  aws-sdk-ec2 (~> 1)
  aws-sdk-ecr (~> 1)
  aws-sdk-iam (~> 1)
  aws-sdk-rds (~> 1)
  aws-sdk-s3 (~> 1)
  bootsnap
  brakeman
  capybara
  capybara-screenshot
  capybara_accessible_selectors!
  caxlsx
  charlock_holmes
  climate_control
  config
  cssbundling-rails
  csv
  cuprite
  debug
  devise
  discard
  dry-cli
  factory_bot_instruments
  factory_bot_rails
  faker
  faraday
  flipper
  flipper-active_record
  flipper-ui
  good_job
  govuk-components
  govuk_design_system_formbuilder
  govuk_markdown
  hotwire-livereload
  its
  jsbundling-rails
  jsonb_accessor
  jwt
  mechanize
  notifications-ruby-client
  okcomputer
  omniauth-rails_csrf_protection
  omniauth_openid_connect
  pagy
  pg
  phonelib
  prettier_print
  propshaft
  pry-rails
  puma
  pundit
  rails (~> 8.0.2)
  rails_semantic_logger
  rainbow
  rladr
  rspec
  rspec-html-matchers
  rspec-rails
  rubocop-govuk
  ruby-prof
  ruby-progressbar
  rubyXL
  rubyzip
  rufo
  sentry-rails
  sentry-ruby
  shoulda-matchers
  simplecov
  solargraph
  solargraph-rails
  splunk-sdk-ruby
  stackprof
  stimulus-rails
  syntax_tree
  syntax_tree-haml
  syntax_tree-rbs
  thruster
  turbo-rails
  tzinfo-data
  uk_postcode
  web-console
  webmock
  wicked
  with_advisory_lock

RUBY VERSION
   ruby 3.4.3p32

BUNDLED WITH
   2.6.2
